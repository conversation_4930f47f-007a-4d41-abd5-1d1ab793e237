#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("開始測試...")

try:
    print("1. 測試基本導入...")
    import requests
    import pandas as pd
    from bs4 import BeautifulSoup
    print("   [OK] 基本套件導入成功")

    print("2. 測試網路連線...")
    response = requests.get("https://www.google.com", timeout=5)
    print(f"   [OK] 網路連線正常 (狀態碼: {response.status_code})")

    print("3. 測試目標網站...")
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}

    # 測試申購網站
    try:
        response = requests.get("https://histock.tw/stock/public.aspx", headers=headers, timeout=10)
        print(f"   申購網站狀態: {response.status_code}")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            table = soup.find('table', {'class': 'gvTB'})
            if table:
                print("   [OK] 找到申購資料表格")
            else:
                print("   [ERROR] 未找到申購資料表格")
    except Exception as e:
        print(f"   [ERROR] 申購網站連線失敗: {e}")

    # 測試競拍網站
    try:
        response = requests.get("https://histock.tw/stock/bid.aspx", headers=headers, timeout=10)
        print(f"   競拍網站狀態: {response.status_code}")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            table = soup.find('table', {'class': 'gvTB'})
            if table:
                print("   [OK] 找到競拍資料表格")
            else:
                print("   [ERROR] 未找到競拍資料表格")
    except Exception as e:
        print(f"   [ERROR] 競拍網站連線失敗: {e}")

    print("4. 測試資料抓取函數...")
    from data.fetch import fetch_stock_data
    public_data, bid_data, error = fetch_stock_data()

    if error:
        print(f"   [ERROR] 資料抓取失敗: {error}")
    else:
        print(f"   [OK] 申購資料: {len(public_data)} 筆")
        print(f"   [OK] 競拍資料: {len(bid_data)} 筆")

        if public_data:
            print("   申購資料範例:")
            sample = public_data[0]
            for key, value in list(sample.items())[:3]:
                print(f"     {key}: {value}")

    print("5. 測試UI模組...")
    import tkinter as tk
    from ui.app import StockApp
    print("   [OK] UI模組導入成功")
    
    print("\n測試完成！")
    
except Exception as e:
    print(f"[ERROR] 測試過程中發生錯誤: {e}")
    import traceback
    traceback.print_exc()
