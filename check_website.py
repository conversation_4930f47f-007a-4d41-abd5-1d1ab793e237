#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import sys
import os

def check_website():
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
    
    print("檢查申購網站...")
    try:
        response = requests.get("https://histock.tw/stock/public.aspx", headers=headers, timeout=10)
        print(f"申購網站狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 檢查是否有 gvTB 表格
            table = soup.find('table', {'class': 'gvTB'})
            if table:
                print("找到 gvTB 表格")
                rows = table.find_all('tr')
                print(f"表格行數: {len(rows)}")
                if len(rows) > 1:
                    first_row = rows[1]  # 跳過標題行
                    cols = first_row.find_all('td')
                    print(f"第一行欄位數: {len(cols)}")
                    if len(cols) > 0:
                        print("前幾個欄位內容:")
                        for i, col in enumerate(cols[:5]):
                            print(f"  欄位 {i}: {col.text.strip()}")
            else:
                print("未找到 gvTB 表格")
                # 檢查其他可能的表格
                all_tables = soup.find_all('table')
                print(f"網頁中總共有 {len(all_tables)} 個表格")
                for i, table in enumerate(all_tables[:3]):
                    class_attr = table.get('class', [])
                    id_attr = table.get('id', '')
                    print(f"  表格 {i}: class={class_attr}, id={id_attr}")
        
    except Exception as e:
        print(f"申購網站檢查失敗: {e}")
    
    print("\n檢查競拍網站...")
    try:
        response = requests.get("https://histock.tw/stock/bid.aspx", headers=headers, timeout=10)
        print(f"競拍網站狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 檢查是否有 gvTB 表格
            table = soup.find('table', {'class': 'gvTB'})
            if table:
                print("找到 gvTB 表格")
                rows = table.find_all('tr')
                print(f"表格行數: {len(rows)}")
                if len(rows) > 1:
                    first_row = rows[1]  # 跳過標題行
                    cols = first_row.find_all('td')
                    print(f"第一行欄位數: {len(cols)}")
                    if len(cols) > 0:
                        print("前幾個欄位內容:")
                        for i, col in enumerate(cols[:5]):
                            print(f"  欄位 {i}: {col.text.strip()}")
            else:
                print("未找到 gvTB 表格")
                # 檢查其他可能的表格
                all_tables = soup.find_all('table')
                print(f"網頁中總共有 {len(all_tables)} 個表格")
                for i, table in enumerate(all_tables[:3]):
                    class_attr = table.get('class', [])
                    id_attr = table.get('id', '')
                    print(f"  表格 {i}: class={class_attr}, id={id_attr}")
        
    except Exception as e:
        print(f"競拍網站檢查失敗: {e}")

if __name__ == "__main__":
    check_website()
