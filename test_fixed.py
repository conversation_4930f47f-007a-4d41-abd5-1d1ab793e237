#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("測試修正後的資料抓取功能...")

try:
    from data.fetch import fetch_stock_data
    
    print("正在抓取股票資料...")
    public_data, bid_data, error = fetch_stock_data()
    
    if error:
        print(f"[ERROR] 資料抓取失敗: {error}")
    else:
        print(f"[OK] 申購資料: {len(public_data)} 筆")
        print(f"[OK] 競拍資料: {len(bid_data)} 筆")
        
        if public_data:
            print("\n申購資料範例:")
            sample = public_data[0]
            for key, value in sample.items():
                print(f"  {key}: {value}")
        
        if bid_data:
            print("\n競拍資料範例:")
            sample = bid_data[0]
            for key, value in sample.items():
                print(f"  {key}: {value}")
        
        print("\n[SUCCESS] 資料抓取成功！")
    
except Exception as e:
    print(f"[ERROR] 測試過程中發生錯誤: {e}")
    import traceback
    traceback.print_exc()
