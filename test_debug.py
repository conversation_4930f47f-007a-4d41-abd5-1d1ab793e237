#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """測試所有模組的導入"""
    print("=== 測試模組導入 ===")
    
    try:
        import tkinter as tk
        print("✅ tkinter 導入成功")
    except Exception as e:
        print(f"❌ tkinter 導入失敗: {e}")
        return False
    
    try:
        import requests
        print("✅ requests 導入成功")
    except Exception as e:
        print(f"❌ requests 導入失敗: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas 導入成功")
    except Exception as e:
        print(f"❌ pandas 導入失敗: {e}")
        return False
    
    try:
        from data.fetch import fetch_stock_data
        print("✅ data.fetch 導入成功")
    except Exception as e:
        print(f"❌ data.fetch 導入失敗: {e}")
        traceback.print_exc()
        return False
    
    try:
        from ui.app import StockApp
        print("✅ ui.app 導入成功")
    except Exception as e:
        print(f"❌ ui.app 導入失敗: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_network():
    """測試網路連線"""
    print("\n=== 測試網路連線 ===")
    
    try:
        import requests
        response = requests.get("https://www.google.com", timeout=5)
        print(f"✅ 網路連線正常 (狀態碼: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ 網路連線失敗: {e}")
        return False

def test_data_fetch():
    """測試資料抓取"""
    print("\n=== 測試資料抓取 ===")

    try:
        from data.fetch import fetch_stock_data
        import requests

        # 先測試目標網站是否可達
        print("測試目標網站連線...")
        test_urls = [
            "https://histock.tw/stock/public.aspx",
            "https://histock.tw/stock/bid.aspx"
        ]

        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}

        for url in test_urls:
            try:
                response = requests.get(url, headers=headers, timeout=10)
                print(f"✅ {url} - 狀態碼: {response.status_code}")
                if response.status_code != 200:
                    print(f"⚠️  網站回應異常，狀態碼: {response.status_code}")
            except Exception as e:
                print(f"❌ {url} - 連線失敗: {e}")
                return False

        print("正在抓取股票資料...")
        public_data, bid_data, error = fetch_stock_data()

        if error:
            print(f"❌ 資料抓取失敗: {error}")
            return False

        print(f"✅ 申購資料筆數: {len(public_data)}")
        print(f"✅ 競拍資料筆數: {len(bid_data)}")

        if len(public_data) == 0 and len(bid_data) == 0:
            print("⚠️  沒有抓取到任何資料，可能是網站結構已變更")
            return False

        if public_data:
            print("申購資料範例:")
            for key, value in list(public_data[0].items())[:3]:
                print(f"  {key}: {value}")

        if bid_data:
            print("競拍資料範例:")
            for key, value in list(bid_data[0].items())[:3]:
                print(f"  {key}: {value}")

        return True

    except Exception as e:
        print(f"❌ 資料抓取異常: {e}")
        traceback.print_exc()
        return False

def test_ui_creation():
    """測試UI創建"""
    print("\n=== 測試UI創建 ===")
    
    try:
        import tkinter as tk
        from ui.app import StockApp
        
        print("創建主視窗...")
        root = tk.Tk()
        root.withdraw()  # 隱藏視窗
        
        print("創建應用程式...")
        app = StockApp(root)
        
        print("✅ UI創建成功")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI創建失敗: {e}")
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("股票應用程式診斷工具")
    print("=" * 50)
    
    # 測試導入
    if not test_imports():
        print("\n❌ 模組導入測試失敗，請檢查依賴套件")
        return
    
    # 測試網路
    if not test_network():
        print("\n❌ 網路連線測試失敗，請檢查網路設定")
        return
    
    # 測試資料抓取
    if not test_data_fetch():
        print("\n❌ 資料抓取測試失敗")
        return
    
    # 測試UI創建
    if not test_ui_creation():
        print("\n❌ UI創建測試失敗")
        return
    
    print("\n" + "=" * 50)
    print("✅ 所有測試通過！應用程式應該可以正常運行")
    print("請嘗試執行: python main.py")

if __name__ == "__main__":
    main()
